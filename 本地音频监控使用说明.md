# 🎵 本地音频监控功能使用说明

## 📖 功能概述

本地音频监控功能允许你直接监控本地电脑的音频设备，而不需要依赖OBS的WebSocket API。这样可以实现更精确的音频电平检测和更快的响应速度。

## ✨ 主要优势

- **独立运行**：不需要OBS连接也能工作
- **实时监控**：直接访问音频设备，响应更快
- **精确检测**：基于实际音频电平，而不是音量设置
- **灵活配置**：支持多个音频源和优先级设置

## 🚀 安装步骤

### 1. 安装依赖库

运行依赖安装脚本：

```bash
python install_audio_deps.py
```

或者手动安装：

```bash
pip install sounddevice numpy pycaw comtypes
```

### 2. 测试功能

运行测试程序：

```bash
python test_local_audio.py
```

## 🎛️ 使用方法

### 基本配置

1. **选择音频设备**
   - 在设备下拉列表中选择要监控的音频输入设备
   - 通常是麦克风、线路输入等

2. **添加音频源**
   - 输入音频源名称（如：真人声音、AI声音、碎片化声音）
   - 设置触发阈值（dB）：通常 -30dB 到 -20dB
   - 设置优先级：0=最高，1=中等，2=最低

3. **配置闪避参数**
   - 闪避音量：其他音频源被压低到的音量（0.01-0.1）
   - 保持时间：停止说话后继续保持闪避的时间（0.5-2秒）

### 高级配置

#### 优先级系统

音频闪避遵循优先级规则：
- **优先级 0（最高）**：真人声音，可以压制所有其他声音
- **优先级 1（中等）**：AI声音，可以压制碎片化声音
- **优先级 2（最低）**：碎片化声音，会被其他声音压制

#### 阈值设置

不同环境下的推荐阈值：
- **安静环境**：-35dB 到 -30dB
- **一般环境**：-30dB 到 -25dB  
- **嘈杂环境**：-25dB 到 -20dB

## 🔧 集成到主程序

### 1. 导入模块

```python
from audio_integration import AudioIntegrationManager, AudioIntegrationWidget
```

### 2. 创建管理器

```python
# 在主窗口初始化中
self.audio_integration = AudioIntegrationManager(self)
```

### 3. 添加配置界面

```python
# 创建配置界面
audio_widget = AudioIntegrationWidget(self.audio_integration)

# 添加到主界面的某个Tab中
self.tab_widget.addTab(audio_widget, "🎵 本地音频监控")
```

### 4. 配置音频源映射

```python
# 添加音频源映射（本地设备名 -> OBS源名）
self.audio_integration.add_audio_source_mapping(
    local_name="真人声音",
    obs_name="麦克风",
    device_id=1,  # 音频设备ID
    threshold_db=-30.0,
    priority=0
)
```

## 🎯 使用场景

### 场景1：直播三重闪避

配置三个音频源：
1. **真人麦克风**（优先级0）- 主播说话时压制所有其他声音
2. **AI语音**（优先级1）- AI回复时压制背景音乐
3. **背景音乐**（优先级2）- 被其他声音压制

### 场景2：录制音频去重

监控多个音频输入：
1. **主麦克风** - 主要录音源
2. **备用麦克风** - 备用录音源
3. **系统音频** - 电脑声音

### 场景3：会议音频管理

自动管理会议音频：
1. **主讲人麦克风** - 最高优先级
2. **参与者麦克风** - 中等优先级
3. **背景音乐** - 最低优先级

## 🐛 故障排除

### 常见问题

#### Q: 无法检测到音频设备？
A: 
1. 确保音频设备已正确连接
2. 检查设备驱动是否正常
3. 尝试在系统音频设置中测试设备
4. 重启程序或重新插拔设备

#### Q: 音频检测不准确？
A:
1. 调整触发阈值（降低阈值提高灵敏度）
2. 检查环境噪音水平
3. 确认音频设备增益设置
4. 测试不同的采样率设置

#### Q: 闪避反应太慢？
A:
1. 减少更新间隔时间
2. 降低音频缓冲区大小
3. 检查系统性能和CPU使用率
4. 关闭不必要的后台程序

#### Q: 程序崩溃或卡死？
A:
1. 检查音频设备是否被其他程序占用
2. 确认所有依赖库版本兼容
3. 以管理员权限运行程序
4. 查看错误日志定位问题

### 性能优化

1. **降低采样率**：如果不需要高质量音频分析，可以降低采样率
2. **增大缓冲区**：减少CPU使用，但会增加延迟
3. **减少监控源**：只监控必要的音频源
4. **优化阈值**：避免频繁的状态切换

## 📊 技术参数

### 默认设置
- **采样率**：44100 Hz
- **缓冲区大小**：1024 samples
- **更新间隔**：100ms
- **默认阈值**：-30dB
- **默认闪避音量**：0.03 (约-30dB)
- **默认保持时间**：0.5秒

### 系统要求
- **操作系统**：Windows 10/11
- **Python**：3.7+
- **内存**：至少 100MB 可用内存
- **CPU**：支持实时音频处理

## 🔄 更新日志

### v1.0 (2025-01-29)
- ✨ 初始版本发布
- 🎵 支持本地音频设备监控
- 🔇 实现三重音频闪避系统
- 🎛️ 提供图形化配置界面
- 🔧 集成到主程序框架

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行测试程序检查功能
3. 检查系统音频设备状态
4. 联系技术支持获取帮助

---

**注意**：本功能需要访问音频设备权限，首次运行时可能需要授权。
