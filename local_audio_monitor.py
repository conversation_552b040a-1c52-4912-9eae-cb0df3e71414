#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地音频监控模块
直接监控本地音频设备，不依赖OBS WebSocket API
"""

import sys
import time
import threading
import numpy as np
from typing import Dict, List, Callable, Optional
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    print("⚠️ sounddevice 未安装，将使用备用方案")

try:
    from pycaw.pycaw import AudioUtilities, AudioSession, ISimpleAudioVolume
    from comtypes import CLSCTX_ALL
    PYCAW_AVAILABLE = True
except ImportError:
    PYCAW_AVAILABLE = False
    print("⚠️ pycaw 未安装，音量控制功能将不可用")

@dataclass
class AudioSource:
    """音频源配置"""
    name: str
    device_id: Optional[int] = None
    threshold_db: float = -30.0
    is_speaking: bool = False
    current_volume_db: float = -60.0
    last_trigger_time: float = 0.0
    priority: int = 0  # 0=最高优先级, 1=中等, 2=最低

class LocalAudioMonitor(QObject):
    """本地音频监控器"""
    
    # 信号定义
    audio_level_changed = pyqtSignal(str, float)  # 音频源名称, 音量dB
    speaking_state_changed = pyqtSignal(str, bool)  # 音频源名称, 是否在说话
    ducking_triggered = pyqtSignal(str, list)  # 触发源名称, 需要闪避的目标列表
    
    def __init__(self):
        super().__init__()
        
        # 音频源配置
        self.audio_sources: Dict[str, AudioSource] = {}
        
        # 闪避配置
        self.ducking_config = {
            "enabled": False,
            "ducking_volume": 0.03,  # 闪避时的音量
            "original_volume": 1.0,   # 原始音量
            "hold_time": 0.5,         # 保持时间（秒）
            "fade_time": 0.2,         # 淡入淡出时间
        }
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.sample_rate = 44100
        self.block_size = 1024
        self.update_interval = 0.1  # 更新间隔（秒）
        
        # 音频流
        self.input_streams = {}
        
        # 系统音量控制
        self.volume_controllers = {}
        
        # 初始化音频设备
        self.init_audio_devices()
        
    def init_audio_devices(self):
        """初始化音频设备"""
        if not SOUNDDEVICE_AVAILABLE:
            print("❌ sounddevice 不可用，无法初始化音频设备")
            return
            
        try:
            # 获取可用的音频设备
            devices = sd.query_devices()
            print("🎵 可用音频设备:")
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    print(f"  {i}: {device['name']} (输入通道: {device['max_input_channels']})")
                    
        except Exception as e:
            print(f"❌ 初始化音频设备失败: {e}")
    
    def get_available_devices(self) -> List[Dict]:
        """获取可用的音频输入设备"""
        if not SOUNDDEVICE_AVAILABLE:
            return []
            
        try:
            devices = sd.query_devices()
            input_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append({
                        'id': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate']
                    })
            return input_devices
        except Exception as e:
            print(f"❌ 获取音频设备失败: {e}")
            return []
    
    def add_audio_source(self, name: str, device_id: int, threshold_db: float = -30.0, priority: int = 0):
        """添加音频源"""
        source = AudioSource(
            name=name,
            device_id=device_id,
            threshold_db=threshold_db,
            priority=priority
        )
        self.audio_sources[name] = source
        print(f"✅ 添加音频源: {name} (设备ID: {device_id}, 阈值: {threshold_db}dB, 优先级: {priority})")
        
        # 如果正在监控，启动这个源的监控
        if self.is_monitoring:
            self.start_source_monitoring(name)
    
    def remove_audio_source(self, name: str):
        """移除音频源"""
        if name in self.audio_sources:
            # 停止监控
            self.stop_source_monitoring(name)
            del self.audio_sources[name]
            print(f"✅ 移除音频源: {name}")
    
    def start_monitoring(self):
        """开始监控所有音频源"""
        if self.is_monitoring:
            print("⚠️ 音频监控已经在运行")
            return
            
        if not SOUNDDEVICE_AVAILABLE:
            print("❌ sounddevice 不可用，无法开始监控")
            return
            
        self.is_monitoring = True
        print("🎵 开始音频监控...")
        
        # 为每个音频源启动监控
        for name in self.audio_sources:
            self.start_source_monitoring(name)
    
    def stop_monitoring(self):
        """停止监控所有音频源"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        print("🔇 停止音频监控...")
        
        # 停止所有音频源的监控
        for name in list(self.audio_sources.keys()):
            self.stop_source_monitoring(name)
    
    def start_source_monitoring(self, name: str):
        """开始监控指定音频源"""
        if name not in self.audio_sources:
            print(f"❌ 音频源 {name} 不存在")
            return
            
        source = self.audio_sources[name]
        if source.device_id is None:
            print(f"❌ 音频源 {name} 没有指定设备ID")
            return
            
        try:
            # 创建音频流
            def audio_callback(indata, frames, time, status):
                if status:
                    print(f"⚠️ 音频流状态: {status}")
                
                # 计算RMS音量
                rms = np.sqrt(np.mean(indata**2))
                
                # 转换为dB
                if rms > 0:
                    db = 20 * np.log10(rms)
                else:
                    db = -60.0
                
                # 更新音频源状态
                self.update_source_level(name, db)
            
            # 启动音频流
            stream = sd.InputStream(
                device=source.device_id,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.block_size,
                callback=audio_callback
            )
            
            stream.start()
            self.input_streams[name] = stream
            print(f"✅ 开始监控音频源: {name}")
            
        except Exception as e:
            print(f"❌ 启动音频源 {name} 监控失败: {e}")
    
    def stop_source_monitoring(self, name: str):
        """停止监控指定音频源"""
        if name in self.input_streams:
            try:
                self.input_streams[name].stop()
                self.input_streams[name].close()
                del self.input_streams[name]
                print(f"✅ 停止监控音频源: {name}")
            except Exception as e:
                print(f"❌ 停止音频源 {name} 监控失败: {e}")
    
    def update_source_level(self, name: str, db: float):
        """更新音频源电平"""
        if name not in self.audio_sources:
            return
            
        source = self.audio_sources[name]
        source.current_volume_db = db
        
        # 发射音量变化信号
        self.audio_level_changed.emit(name, db)
        
        # 检查是否触发说话状态变化
        current_time = time.time()
        was_speaking = source.is_speaking
        
        if db > source.threshold_db:
            # 超过阈值，开始说话
            source.last_trigger_time = current_time
            if not was_speaking:
                source.is_speaking = True
                self.speaking_state_changed.emit(name, True)
                print(f"🎤 {name} 开始说话 ({db:.1f}dB)")
                
                # 触发闪避逻辑
                if self.ducking_config["enabled"]:
                    self.trigger_ducking(name)
        else:
            # 低于阈值，检查保持时间
            hold_time = self.ducking_config["hold_time"]
            if was_speaking and (current_time - source.last_trigger_time) > hold_time:
                source.is_speaking = False
                self.speaking_state_changed.emit(name, False)
                print(f"🔇 {name} 停止说话")
                
                # 检查是否需要恢复音量
                if self.ducking_config["enabled"]:
                    self.check_ducking_recovery()
    
    def trigger_ducking(self, trigger_source: str):
        """触发音频闪避"""
        if not self.ducking_config["enabled"]:
            return
            
        # 获取触发源的优先级
        trigger_priority = self.audio_sources[trigger_source].priority
        
        # 找出需要闪避的目标源（优先级更低的）
        targets_to_duck = []
        for name, source in self.audio_sources.items():
            if name != trigger_source and source.priority > trigger_priority:
                targets_to_duck.append(name)
        
        if targets_to_duck:
            print(f"🔇 {trigger_source} 触发闪避，目标: {targets_to_duck}")
            self.ducking_triggered.emit(trigger_source, targets_to_duck)
            
            # 执行音量控制
            for target in targets_to_duck:
                self.set_source_volume(target, self.ducking_config["ducking_volume"])
    
    def check_ducking_recovery(self):
        """检查是否需要恢复闪避"""
        if not self.ducking_config["enabled"]:
            return
            
        # 检查是否还有人在说话
        speaking_sources = [name for name, source in self.audio_sources.items() if source.is_speaking]
        
        if not speaking_sources:
            # 没有人说话，恢复所有音量
            print("🔊 恢复所有音频源音量")
            for name in self.audio_sources:
                self.set_source_volume(name, self.ducking_config["original_volume"])
        else:
            # 还有人说话，重新计算闪避
            highest_priority_speaker = min(speaking_sources, 
                                         key=lambda x: self.audio_sources[x].priority)
            self.trigger_ducking(highest_priority_speaker)
    
    def set_source_volume(self, source_name: str, volume: float):
        """设置音频源音量（这里需要根据实际情况实现）"""
        # 这个方法需要根据具体的音频控制需求来实现
        # 可能需要控制系统音量、应用程序音量或OBS音量
        print(f"🔊 设置 {source_name} 音量为 {volume:.3f}")
        
        # 如果有pycaw，可以控制系统音量
        if PYCAW_AVAILABLE:
            try:
                # 这里可以实现具体的音量控制逻辑
                pass
            except Exception as e:
                print(f"❌ 设置音量失败: {e}")
    
    def set_ducking_enabled(self, enabled: bool):
        """启用/禁用音频闪避"""
        self.ducking_config["enabled"] = enabled
        print(f"🎯 音频闪避: {'启用' if enabled else '禁用'}")
        
        if not enabled:
            # 禁用时恢复所有音量
            for name in self.audio_sources:
                self.set_source_volume(name, self.ducking_config["original_volume"])
    
    def set_ducking_volume(self, volume: float):
        """设置闪避音量"""
        self.ducking_config["ducking_volume"] = max(0.0, min(1.0, volume))
        print(f"🔇 闪避音量设置为: {self.ducking_config['ducking_volume']:.3f}")
    
    def set_hold_time(self, hold_time: float):
        """设置保持时间"""
        self.ducking_config["hold_time"] = max(0.1, hold_time)
        print(f"⏱️ 保持时间设置为: {self.ducking_config['hold_time']:.1f}秒")
    
    def get_source_status(self) -> Dict:
        """获取所有音频源状态"""
        status = {}
        for name, source in self.audio_sources.items():
            status[name] = {
                'is_speaking': source.is_speaking,
                'current_db': source.current_volume_db,
                'threshold_db': source.threshold_db,
                'priority': source.priority
            }
        return status
