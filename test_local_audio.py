#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地音频监控测试脚本
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QProgressBar, QTextEdit
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

from local_audio_monitor import LocalAudioMonitor

class AudioMonitorTestWindow(QMainWindow):
    """音频监控测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("本地音频监控测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建音频监控器
        self.audio_monitor = LocalAudioMonitor()
        
        # 连接信号
        self.audio_monitor.audio_level_changed.connect(self.on_audio_level_changed)
        self.audio_monitor.speaking_state_changed.connect(self.on_speaking_state_changed)
        self.audio_monitor.ducking_triggered.connect(self.on_ducking_triggered)
        
        # 初始化UI
        self.init_ui()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(100)  # 每100ms更新一次
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎵 本地音频监控测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 设备选择区域
        device_layout = QHBoxLayout()
        
        device_layout.addWidget(QLabel("音频设备:"))
        self.device_combo = QComboBox()
        device_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新设备")
        refresh_btn.clicked.connect(self.refresh_devices)
        device_layout.addWidget(refresh_btn)
        
        layout.addLayout(device_layout)
        
        # 音频源配置区域
        source_layout = QHBoxLayout()
        
        source_layout.addWidget(QLabel("音频源名称:"))
        self.source_name_combo = QComboBox()
        self.source_name_combo.setEditable(True)
        self.source_name_combo.addItems(["真人声音", "AI声音", "碎片化声音", "麦克风", "桌面音频"])
        source_layout.addWidget(self.source_name_combo)
        
        source_layout.addWidget(QLabel("阈值(dB):"))
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-60.0, 0.0)
        self.threshold_spin.setValue(-30.0)
        self.threshold_spin.setSuffix(" dB")
        source_layout.addWidget(self.threshold_spin)
        
        source_layout.addWidget(QLabel("优先级:"))
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(0, 2)
        self.priority_spin.setValue(0)
        source_layout.addWidget(self.priority_spin)
        
        add_source_btn = QPushButton("➕ 添加音频源")
        add_source_btn.clicked.connect(self.add_audio_source)
        source_layout.addWidget(add_source_btn)
        
        layout.addLayout(source_layout)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🎵 开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("🔇 停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        # 闪避配置区域
        ducking_layout = QVBoxLayout()
        
        self.ducking_enabled = QCheckBox("启用音频闪避")
        self.ducking_enabled.stateChanged.connect(self.on_ducking_enabled_changed)
        ducking_layout.addWidget(self.ducking_enabled)
        
        ducking_params_layout = QHBoxLayout()
        
        ducking_params_layout.addWidget(QLabel("闪避音量:"))
        self.ducking_volume_spin = QDoubleSpinBox()
        self.ducking_volume_spin.setRange(0.0, 1.0)
        self.ducking_volume_spin.setValue(0.03)
        self.ducking_volume_spin.setSingleStep(0.01)
        self.ducking_volume_spin.valueChanged.connect(self.on_ducking_volume_changed)
        ducking_params_layout.addWidget(self.ducking_volume_spin)
        
        ducking_params_layout.addWidget(QLabel("保持时间:"))
        self.hold_time_spin = QDoubleSpinBox()
        self.hold_time_spin.setRange(0.1, 5.0)
        self.hold_time_spin.setValue(0.5)
        self.hold_time_spin.setSuffix(" 秒")
        self.hold_time_spin.valueChanged.connect(self.on_hold_time_changed)
        ducking_params_layout.addWidget(self.hold_time_spin)
        
        ducking_layout.addLayout(ducking_params_layout)
        layout.addLayout(ducking_layout)
        
        # 状态显示区域
        status_layout = QVBoxLayout()
        
        status_layout.addWidget(QLabel("📊 音频源状态:"))
        
        # 音频源状态显示
        self.source_status_layout = QVBoxLayout()
        status_layout.addLayout(self.source_status_layout)
        
        layout.addLayout(status_layout)
        
        # 日志区域
        layout.addWidget(QLabel("📝 日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 存储音频源状态显示组件
        self.source_widgets = {}

        # 初始化设备列表
        self.refresh_devices()
        
    def refresh_devices(self):
        """刷新音频设备列表"""
        self.device_combo.clear()
        devices = self.audio_monitor.get_available_devices()
        
        for device in devices:
            self.device_combo.addItem(f"{device['name']} (ID: {device['id']})", device['id'])
        
        self.log(f"发现 {len(devices)} 个音频输入设备")
        
    def add_audio_source(self):
        """添加音频源"""
        if self.device_combo.currentData() is None:
            self.log("❌ 请先选择音频设备")
            return
            
        name = self.source_name_combo.currentText().strip()
        if not name:
            self.log("❌ 请输入音频源名称")
            return
            
        device_id = self.device_combo.currentData()
        threshold_db = self.threshold_spin.value()
        priority = self.priority_spin.value()
        
        # 添加到监控器
        self.audio_monitor.add_audio_source(name, device_id, threshold_db, priority)
        
        # 创建状态显示组件
        self.create_source_widget(name)
        
        self.log(f"✅ 添加音频源: {name}")
        
    def create_source_widget(self, name: str):
        """创建音频源状态显示组件"""
        if name in self.source_widgets:
            return
            
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 名称标签
        name_label = QLabel(name)
        name_label.setMinimumWidth(100)
        name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(name_label)
        
        # 音量进度条
        volume_bar = QProgressBar()
        volume_bar.setRange(0, 100)
        volume_bar.setValue(0)
        volume_bar.setTextVisible(True)
        volume_bar.setFormat("%.1f dB")
        layout.addWidget(volume_bar)
        
        # 说话状态指示器
        speaking_label = QLabel("🔇")
        speaking_label.setMinimumWidth(30)
        speaking_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(speaking_label)
        
        # 移除按钮
        remove_btn = QPushButton("❌")
        remove_btn.setMaximumWidth(30)
        remove_btn.clicked.connect(lambda: self.remove_audio_source(name))
        layout.addWidget(remove_btn)
        
        self.source_widgets[name] = {
            'widget': widget,
            'volume_bar': volume_bar,
            'speaking_label': speaking_label
        }
        
        self.source_status_layout.addWidget(widget)
        
    def remove_audio_source(self, name: str):
        """移除音频源"""
        if name in self.source_widgets:
            # 移除UI组件
            widget_info = self.source_widgets[name]
            self.source_status_layout.removeWidget(widget_info['widget'])
            widget_info['widget'].deleteLater()
            del self.source_widgets[name]
            
            # 从监控器移除
            self.audio_monitor.remove_audio_source(name)
            
            self.log(f"✅ 移除音频源: {name}")
    
    def start_monitoring(self):
        """开始监控"""
        self.audio_monitor.start_monitoring()
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log("🎵 开始音频监控")
        
    def stop_monitoring(self):
        """停止监控"""
        self.audio_monitor.stop_monitoring()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log("🔇 停止音频监控")
        
    def on_ducking_enabled_changed(self, state):
        """闪避启用状态改变"""
        enabled = state == Qt.Checked
        self.audio_monitor.set_ducking_enabled(enabled)
        self.log(f"🎯 音频闪避: {'启用' if enabled else '禁用'}")
        
    def on_ducking_volume_changed(self, value):
        """闪避音量改变"""
        self.audio_monitor.set_ducking_volume(value)
        
    def on_hold_time_changed(self, value):
        """保持时间改变"""
        self.audio_monitor.set_hold_time(value)
        
    def on_audio_level_changed(self, name: str, db: float):
        """音频电平改变"""
        if name in self.source_widgets:
            volume_bar = self.source_widgets[name]['volume_bar']
            # 将dB转换为0-100的进度值 (-60dB到0dB)
            progress = max(0, min(100, (db + 60) * 100 / 60))
            volume_bar.setValue(int(progress))
            volume_bar.setFormat(f"{db:.1f} dB")
            
    def on_speaking_state_changed(self, name: str, is_speaking: bool):
        """说话状态改变"""
        if name in self.source_widgets:
            speaking_label = self.source_widgets[name]['speaking_label']
            speaking_label.setText("🎤" if is_speaking else "🔇")
            
        self.log(f"{'🎤' if is_speaking else '🔇'} {name}: {'开始说话' if is_speaking else '停止说话'}")
        
    def on_ducking_triggered(self, trigger_source: str, targets: list):
        """闪避触发"""
        self.log(f"🔇 {trigger_source} 触发闪避，目标: {', '.join(targets)}")
        
    def update_status(self):
        """更新状态显示"""
        # 这里可以添加更多的状态更新逻辑
        pass
        
    def log(self, message: str):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 检查依赖
    try:
        import sounddevice as sd
        print("✅ sounddevice 可用")
    except ImportError:
        print("❌ sounddevice 不可用，请安装: pip install sounddevice")
        
    try:
        from pycaw.pycaw import AudioUtilities
        print("✅ pycaw 可用")
    except ImportError:
        print("❌ pycaw 不可用，请安装: pip install pycaw")
    
    window = AudioMonitorTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
