#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频闪避功能测试脚本
"""

import sys
import math
import time

def test_volume_conversion():
    """测试音量转换功能"""
    print("=== 测试音量转换功能 ===")
    
    # 测试线性音量到dB的转换
    test_volumes = [0.0, 0.01, 0.03, 0.1, 0.5, 1.0]
    
    for volume in test_volumes:
        if volume > 0:
            db = 20 * math.log10(volume)
        else:
            db = -60.0
        print(f"音量 {volume:.3f} -> {db:.1f} dB")
    
    print()

def test_ducking_logic():
    """测试闪避逻辑"""
    print("=== 测试闪避逻辑 ===")
    
    # 模拟音频闪避控制状态
    ducking_control = {
        "enabled": True,
        "trigger_source": "麦克风",
        "target_sources": ["桌面音频", "音乐"],
        "ducking_volume": 0.03,
        "original_volume": 1.00,
        "threshold_db": -30.0,
        "is_ducking": False,
        "last_trigger_time": 0.0,
        "hold_time": 0.5,
    }
    
    # 模拟音量检测序列
    volume_sequence = [
        (0.001, "静音状态"),
        (0.01, "低音量"),
        (0.1, "触发闪避"),  # -20dB，超过-30dB阈值
        (0.05, "持续触发"),
        (0.001, "音量降低"),
        (0.001, "保持静音"),
    ]
    
    current_time = time.time()
    
    for volume, description in volume_sequence:
        # 转换为dB
        if volume > 0:
            current_db = 20 * math.log10(volume)
        else:
            current_db = -60.0
        
        # 判断是否需要触发闪避
        should_duck = current_db > ducking_control["threshold_db"]
        
        print(f"{description}: 音量={volume:.3f} ({current_db:.1f}dB)")
        
        if should_duck and not ducking_control["is_ducking"]:
            # 触发闪避
            ducking_control["is_ducking"] = True
            ducking_control["last_trigger_time"] = current_time
            print(f"  -> 🔇 触发闪避！目标音量降至 {ducking_control['ducking_volume']:.3f}")
            
        elif not should_duck and ducking_control["is_ducking"]:
            # 检查是否应该恢复
            hold_time = ducking_control["hold_time"]
            if current_time - ducking_control["last_trigger_time"] > hold_time:
                ducking_control["is_ducking"] = False
                print(f"  -> 🔊 恢复音量！目标音量恢复至 {ducking_control['original_volume']:.3f}")
            else:
                print(f"  -> ⏳ 等待保持时间结束...")
        
        elif ducking_control["is_ducking"]:
            print(f"  -> 🔇 保持闪避状态")
        else:
            print(f"  -> 🔊 正常音量")
        
        current_time += 1.0  # 模拟时间推进
        print()

def test_parameter_validation():
    """测试参数验证"""
    print("=== 测试参数验证 ===")
    
    # 测试各种参数组合
    test_cases = [
        {"ducking_volume": 0.03, "original_volume": 1.0, "threshold_db": -30.0, "valid": True},
        {"ducking_volume": 0.0, "original_volume": 1.0, "threshold_db": -30.0, "valid": True},
        {"ducking_volume": 1.0, "original_volume": 1.0, "threshold_db": -30.0, "valid": True},
        {"ducking_volume": 0.03, "original_volume": 0.0, "threshold_db": -30.0, "valid": False},
        {"ducking_volume": 0.03, "original_volume": 1.0, "threshold_db": 0.0, "valid": True},
        {"ducking_volume": 0.03, "original_volume": 1.0, "threshold_db": -60.0, "valid": True},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"测试用例 {i+1}:")
        print(f"  闪避音量: {case['ducking_volume']:.3f}")
        print(f"  原始音量: {case['original_volume']:.3f}")
        print(f"  触发阈值: {case['threshold_db']:.1f} dB")
        
        # 简单的参数验证
        valid = True
        if case['ducking_volume'] < 0 or case['ducking_volume'] > 1:
            valid = False
            print("  ❌ 闪避音量超出范围 [0.0, 1.0]")
        if case['original_volume'] <= 0 or case['original_volume'] > 1:
            valid = False
            print("  ❌ 原始音量超出范围 (0.0, 1.0]")
        if case['threshold_db'] > 0:
            print("  ⚠️  触发阈值为正值，可能导致频繁触发")
        
        if valid:
            print("  ✅ 参数验证通过")
        else:
            print("  ❌ 参数验证失败")
        
        expected = case.get('valid', True)
        if valid == expected:
            print("  ✅ 测试结果符合预期")
        else:
            print("  ❌ 测试结果不符合预期")
        print()

def main():
    """主测试函数"""
    print("🔇 音频闪避功能测试")
    print("=" * 50)
    
    test_volume_conversion()
    test_ducking_logic()
    test_parameter_validation()
    
    print("=" * 50)
    print("✅ 所有测试完成！")

if __name__ == "__main__":
    main()
