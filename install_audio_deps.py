#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频依赖安装脚本
自动安装本地音频监控所需的依赖库
"""

import subprocess
import sys
import os
import importlib.util

def check_package(package_name):
    """检查包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_package(package_name, pip_name=None):
    """安装包"""
    if pip_name is None:
        pip_name = package_name
    
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🎵 音频依赖安装脚本")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("sounddevice", "sounddevice"),
        ("numpy", "numpy"),
        ("pycaw", "pycaw"),
        ("comtypes", "comtypes"),
    ]
    
    installed_count = 0
    failed_count = 0
    
    for package_name, pip_name in packages:
        print(f"\n检查 {package_name}...")
        
        if check_package(package_name):
            print(f"✅ {package_name} 已安装")
            installed_count += 1
        else:
            print(f"⚠️ {package_name} 未安装，正在安装...")
            if install_package(package_name, pip_name):
                installed_count += 1
            else:
                failed_count += 1
    
    print("\n" + "=" * 50)
    print(f"安装完成！成功: {installed_count}, 失败: {failed_count}")
    
    if failed_count > 0:
        print("\n❌ 部分依赖安装失败，请手动安装:")
        print("pip install sounddevice numpy pycaw comtypes")
    else:
        print("\n✅ 所有依赖安装成功！")
        
    # 测试导入
    print("\n🧪 测试导入...")
    test_imports()

def test_imports():
    """测试导入所有依赖"""
    imports = [
        ("sounddevice", "import sounddevice as sd"),
        ("numpy", "import numpy as np"),
        ("pycaw", "from pycaw.pycaw import AudioUtilities"),
        ("comtypes", "import comtypes"),
    ]
    
    for name, import_code in imports:
        try:
            exec(import_code)
            print(f"✅ {name} 导入成功")
        except ImportError as e:
            print(f"❌ {name} 导入失败: {e}")
        except Exception as e:
            print(f"⚠️ {name} 导入异常: {e}")

if __name__ == "__main__":
    main()
