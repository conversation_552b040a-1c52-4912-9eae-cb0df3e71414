#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频集成模块
将本地音频监控功能集成到主程序中
"""

import time
from typing import Dict, List, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton, QCheckBox, QDoubleSpinBox, QSpinBox, QGroupBox, QFormLayout, QProgressBar

try:
    from local_audio_monitor import LocalAudioMonitor
    LOCAL_AUDIO_AVAILABLE = True
except ImportError:
    LOCAL_AUDIO_AVAILABLE = False
    print("⚠️ 本地音频监控模块不可用")

class AudioIntegrationManager(QObject):
    """音频集成管理器"""
    
    # 信号定义
    ducking_state_changed = pyqtSignal(str, bool)  # 音频源名称, 是否需要闪避
    volume_control_needed = pyqtSignal(str, float)  # 音频源名称, 目标音量
    
    def __init__(self, main_window=None):
        super().__init__()
        
        self.main_window = main_window
        
        # 本地音频监控器
        self.local_monitor = None
        if LOCAL_AUDIO_AVAILABLE:
            self.local_monitor = LocalAudioMonitor()
            self.setup_local_monitor_connections()
        
        # 音频源映射 (本地设备名 -> OBS源名)
        self.source_mapping = {}
        
        # 控制模式: 'obs' 或 'local'
        self.control_mode = 'obs'
        
        # 状态管理
        self.is_active = False
        
    def setup_local_monitor_connections(self):
        """设置本地监控器连接"""
        if not self.local_monitor:
            return
            
        self.local_monitor.audio_level_changed.connect(self.on_local_audio_level_changed)
        self.local_monitor.speaking_state_changed.connect(self.on_local_speaking_state_changed)
        self.local_monitor.ducking_triggered.connect(self.on_local_ducking_triggered)
    
    def set_control_mode(self, mode: str):
        """设置控制模式"""
        if mode not in ['obs', 'local']:
            print(f"❌ 无效的控制模式: {mode}")
            return
            
        old_mode = self.control_mode
        self.control_mode = mode
        
        print(f"🔄 音频控制模式切换: {old_mode} -> {mode}")
        
        # 如果正在运行，重新启动
        if self.is_active:
            self.stop()
            self.start()
    
    def add_audio_source_mapping(self, local_name: str, obs_name: str, device_id: int, threshold_db: float = -30.0, priority: int = 0):
        """添加音频源映射"""
        self.source_mapping[local_name] = {
            'obs_name': obs_name,
            'device_id': device_id,
            'threshold_db': threshold_db,
            'priority': priority
        }
        
        # 如果使用本地监控，添加到本地监控器
        if self.control_mode == 'local' and self.local_monitor:
            self.local_monitor.add_audio_source(local_name, device_id, threshold_db, priority)
        
        print(f"✅ 添加音频源映射: {local_name} -> {obs_name}")
    
    def remove_audio_source_mapping(self, local_name: str):
        """移除音频源映射"""
        if local_name in self.source_mapping:
            del self.source_mapping[local_name]
            
            # 从本地监控器移除
            if self.local_monitor:
                self.local_monitor.remove_audio_source(local_name)
            
            print(f"✅ 移除音频源映射: {local_name}")
    
    def start(self):
        """启动音频监控"""
        if self.is_active:
            print("⚠️ 音频监控已经在运行")
            return
            
        self.is_active = True
        
        if self.control_mode == 'local' and self.local_monitor:
            # 启动本地监控
            self.local_monitor.start_monitoring()
            print("🎵 启动本地音频监控")
        elif self.control_mode == 'obs':
            # 启动OBS监控（使用现有的三重闪避逻辑）
            if self.main_window:
                self.main_window.triple_ducking_control["enabled"] = True
                if hasattr(self.main_window, 'monitor_triple_audio_levels'):
                    # 启动OBS监控定时器
                    timer = self.main_window.triple_ducking_control.get("monitor_timer")
                    if timer:
                        timer.start(100)  # 每100ms检查一次
            print("🎵 启动OBS音频监控")
    
    def stop(self):
        """停止音频监控"""
        if not self.is_active:
            return
            
        self.is_active = False
        
        if self.local_monitor:
            self.local_monitor.stop_monitoring()
        
        if self.main_window:
            self.main_window.triple_ducking_control["enabled"] = False
            timer = self.main_window.triple_ducking_control.get("monitor_timer")
            if timer:
                timer.stop()
        
        print("🔇 停止音频监控")
    
    def set_ducking_enabled(self, enabled: bool):
        """启用/禁用音频闪避"""
        if self.local_monitor:
            self.local_monitor.set_ducking_enabled(enabled)
        
        if self.main_window:
            self.main_window.triple_ducking_control["enabled"] = enabled
    
    def set_ducking_parameters(self, ducking_volume: float, hold_time: float):
        """设置闪避参数"""
        if self.local_monitor:
            self.local_monitor.set_ducking_volume(ducking_volume)
            self.local_monitor.set_hold_time(hold_time)
        
        if self.main_window:
            self.main_window.triple_ducking_control["ducking_volume"] = ducking_volume
            self.main_window.triple_ducking_control["hold_time"] = hold_time
    
    def get_available_devices(self) -> List[Dict]:
        """获取可用的音频设备"""
        if self.local_monitor:
            return self.local_monitor.get_available_devices()
        return []
    
    def on_local_audio_level_changed(self, name: str, db: float):
        """本地音频电平改变"""
        # 可以在这里添加音频电平显示逻辑
        pass
    
    def on_local_speaking_state_changed(self, name: str, is_speaking: bool):
        """本地说话状态改变"""
        print(f"🎤 {name}: {'开始说话' if is_speaking else '停止说话'}")
        
        # 发射信号给主窗口
        self.ducking_state_changed.emit(name, is_speaking)
    
    def on_local_ducking_triggered(self, trigger_source: str, targets: List[str]):
        """本地闪避触发"""
        print(f"🔇 {trigger_source} 触发闪避，目标: {targets}")
        
        # 控制OBS音量
        if self.main_window and hasattr(self.main_window, 'send_request'):
            ducking_volume = self.local_monitor.ducking_config["ducking_volume"]
            
            for target in targets:
                # 查找对应的OBS源名称
                obs_name = None
                for local_name, mapping in self.source_mapping.items():
                    if local_name == target:
                        obs_name = mapping['obs_name']
                        break
                
                if obs_name:
                    # 发送OBS音量控制请求
                    self.main_window.send_request("SetInputVolume", {
                        "inputName": obs_name,
                        "inputVolumeMul": ducking_volume
                    })
                    print(f"🔊 设置OBS源 {obs_name} 音量为 {ducking_volume:.3f}")

class AudioIntegrationWidget(QWidget):
    """音频集成配置界面"""
    
    def __init__(self, integration_manager: AudioIntegrationManager):
        super().__init__()
        
        self.integration_manager = integration_manager
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制模式选择
        mode_group = QGroupBox("🎛️ 控制模式")
        mode_layout = QFormLayout(mode_group)
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["OBS WebSocket", "本地音频设备"])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        mode_layout.addRow("控制模式:", self.mode_combo)
        
        layout.addWidget(mode_group)
        
        # 音频源配置
        source_group = QGroupBox("🎤 音频源配置")
        source_layout = QVBoxLayout(source_group)
        
        # 添加音频源
        add_source_layout = QHBoxLayout()
        
        self.device_combo = QComboBox()
        self.refresh_devices()
        add_source_layout.addWidget(QLabel("设备:"))
        add_source_layout.addWidget(self.device_combo)
        
        self.local_name_combo = QComboBox()
        self.local_name_combo.setEditable(True)
        self.local_name_combo.addItems(["真人声音", "AI声音", "碎片化声音"])
        add_source_layout.addWidget(QLabel("本地名称:"))
        add_source_layout.addWidget(self.local_name_combo)
        
        self.obs_name_combo = QComboBox()
        self.obs_name_combo.setEditable(True)
        add_source_layout.addWidget(QLabel("OBS源名:"))
        add_source_layout.addWidget(self.obs_name_combo)
        
        add_btn = QPushButton("➕ 添加")
        add_btn.clicked.connect(self.add_audio_source)
        add_source_layout.addWidget(add_btn)
        
        source_layout.addLayout(add_source_layout)
        
        # 音频源参数
        params_layout = QHBoxLayout()
        
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-60.0, 0.0)
        self.threshold_spin.setValue(-30.0)
        self.threshold_spin.setSuffix(" dB")
        params_layout.addWidget(QLabel("阈值:"))
        params_layout.addWidget(self.threshold_spin)
        
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(0, 2)
        self.priority_spin.setValue(0)
        params_layout.addWidget(QLabel("优先级:"))
        params_layout.addWidget(self.priority_spin)
        
        source_layout.addLayout(params_layout)
        
        layout.addWidget(source_group)
        
        # 闪避配置
        ducking_group = QGroupBox("🔇 闪避配置")
        ducking_layout = QFormLayout(ducking_group)
        
        self.ducking_enabled = QCheckBox("启用音频闪避")
        self.ducking_enabled.stateChanged.connect(self.on_ducking_enabled_changed)
        ducking_layout.addRow(self.ducking_enabled)
        
        self.ducking_volume_spin = QDoubleSpinBox()
        self.ducking_volume_spin.setRange(0.0, 1.0)
        self.ducking_volume_spin.setValue(0.03)
        self.ducking_volume_spin.setSingleStep(0.01)
        self.ducking_volume_spin.valueChanged.connect(self.on_ducking_params_changed)
        ducking_layout.addRow("闪避音量:", self.ducking_volume_spin)
        
        self.hold_time_spin = QDoubleSpinBox()
        self.hold_time_spin.setRange(0.1, 5.0)
        self.hold_time_spin.setValue(0.5)
        self.hold_time_spin.setSuffix(" 秒")
        self.hold_time_spin.valueChanged.connect(self.on_ducking_params_changed)
        ducking_layout.addRow("保持时间:", self.hold_time_spin)
        
        layout.addWidget(ducking_group)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🎵 开始监控")
        self.start_btn.clicked.connect(self.integration_manager.start)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("🔇 停止监控")
        self.stop_btn.clicked.connect(self.integration_manager.stop)
        control_layout.addWidget(self.stop_btn)
        
        refresh_btn = QPushButton("🔄 刷新设备")
        refresh_btn.clicked.connect(self.refresh_devices)
        control_layout.addWidget(refresh_btn)
        
        layout.addLayout(control_layout)
        
        # 状态显示
        self.status_layout = QVBoxLayout()
        layout.addLayout(self.status_layout)
        
    def refresh_devices(self):
        """刷新音频设备列表"""
        self.device_combo.clear()
        devices = self.integration_manager.get_available_devices()
        
        for device in devices:
            self.device_combo.addItem(f"{device['name']} (ID: {device['id']})", device['id'])
    
    def add_audio_source(self):
        """添加音频源"""
        if self.device_combo.currentData() is None:
            print("❌ 请先选择音频设备")
            return
            
        local_name = self.local_name_combo.currentText().strip()
        obs_name = self.obs_name_combo.currentText().strip()
        
        if not local_name or not obs_name:
            print("❌ 请输入完整的音频源名称")
            return
            
        device_id = self.device_combo.currentData()
        threshold_db = self.threshold_spin.value()
        priority = self.priority_spin.value()
        
        self.integration_manager.add_audio_source_mapping(
            local_name, obs_name, device_id, threshold_db, priority
        )
    
    def on_mode_changed(self, text):
        """控制模式改变"""
        mode = 'local' if text == "本地音频设备" else 'obs'
        self.integration_manager.set_control_mode(mode)
    
    def on_ducking_enabled_changed(self, state):
        """闪避启用状态改变"""
        enabled = state == 2  # Qt.Checked
        self.integration_manager.set_ducking_enabled(enabled)
    
    def on_ducking_params_changed(self):
        """闪避参数改变"""
        ducking_volume = self.ducking_volume_spin.value()
        hold_time = self.hold_time_spin.value()
        self.integration_manager.set_ducking_parameters(ducking_volume, hold_time)
